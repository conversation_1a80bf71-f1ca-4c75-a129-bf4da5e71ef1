{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/official-kaizen-project/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/official-kaizen-project/src/components/ui/kaizen-logo.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface KaizenLogoProps {\n  className?: string\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n}\n\nconst sizeClasses = {\n  sm: 'h-6 w-6',\n  md: 'h-8 w-8',\n  lg: 'h-10 w-10',\n  xl: 'h-12 w-12'\n}\n\nexport function KaizenLogo({ className, size = 'md' }: KaizenLogoProps) {\n  return (\n    <svg\n      className={cn(sizeClasses[size], 'text-blue-500', className)}\n      viewBox=\"0 0 100 100\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      {/* Outer ring with gradient */}\n      <defs>\n        <linearGradient id=\"kaizenGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" stopColor=\"#3B82F6\" />\n          <stop offset=\"50%\" stopColor=\"#1D4ED8\" />\n          <stop offset=\"100%\" stopColor=\"#1E40AF\" />\n        </linearGradient>\n        <filter id=\"glow\">\n          <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n          <feMerge> \n            <feMergeNode in=\"coloredBlur\"/>\n            <feMergeNode in=\"SourceGraphic\"/>\n          </feMerge>\n        </filter>\n      </defs>\n      \n      {/* Background circle */}\n      <circle\n        cx=\"50\"\n        cy=\"50\"\n        r=\"45\"\n        fill=\"url(#kaizenGradient)\"\n        filter=\"url(#glow)\"\n        className=\"drop-shadow-lg\"\n      />\n      \n      {/* Inner design - stylized \"K\" and \"AI\" */}\n      <g fill=\"white\">\n        {/* K letter */}\n        <path d=\"M25 25 L25 75 L30 75 L30 52 L42 75 L48 75 L35 50 L47 25 L41 25 L30 45 L30 25 Z\" />\n        \n        {/* AI letters */}\n        <path d=\"M55 25 L50 75 L55 75 L56 65 L64 65 L65 75 L70 75 L65 25 L55 25 Z M57 35 L63 55 L57 55 L57 35 Z\" />\n        <rect x=\"75\" y=\"25\" width=\"5\" height=\"50\" />\n        <rect x=\"75\" y=\"45\" width=\"10\" height=\"5\" />\n      </g>\n      \n      {/* Accent dots */}\n      <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"white\" opacity=\"0.8\" />\n      <circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"white\" opacity=\"0.8\" />\n      <circle cx=\"80\" cy=\"20\" r=\"1.5\" fill=\"white\" opacity=\"0.6\" />\n    </svg>\n  )\n}\n\nexport function KaizenWordmark({ className }: { className?: string }) {\n  return (\n    <div className={cn('flex items-center gap-3', className)}>\n      <KaizenLogo size=\"lg\" />\n      <span className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent\">\n        KAIZEN\n      </span>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAOA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,WAAW,KAA2C;QAA3C,EAAE,SAAS,EAAE,OAAO,IAAI,EAAmB,GAA3C;IACzB,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,WAAW,CAAC,KAAK,EAAE,iBAAiB;QAClD,SAAQ;QACR,MAAK;QACL,OAAM;;0BAGN,6LAAC;;kCACC,6LAAC;wBAAe,IAAG;wBAAiB,IAAG;wBAAK,IAAG;wBAAK,IAAG;wBAAO,IAAG;;0CAC/D,6LAAC;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CAC5B,6LAAC;gCAAK,QAAO;gCAAM,WAAU;;;;;;0CAC7B,6LAAC;gCAAK,QAAO;gCAAO,WAAU;;;;;;;;;;;;kCAEhC,6LAAC;wBAAO,IAAG;;0CACT,6LAAC;gCAAe,cAAa;gCAAI,QAAO;;;;;;0CACxC,6LAAC;;kDACC,6LAAC;wCAAY,IAAG;;;;;;kDAChB,6LAAC;wCAAY,IAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,WAAU;;;;;;0BAIZ,6LAAC;gBAAE,MAAK;;kCAEN,6LAAC;wBAAK,GAAE;;;;;;kCAGR,6LAAC;wBAAK,GAAE;;;;;;kCACR,6LAAC;wBAAK,GAAE;wBAAK,GAAE;wBAAK,OAAM;wBAAI,QAAO;;;;;;kCACrC,6LAAC;wBAAK,GAAE;wBAAK,GAAE;wBAAK,OAAM;wBAAK,QAAO;;;;;;;;;;;;0BAIxC,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;gBAAQ,SAAQ;;;;;;0BACnD,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;gBAAQ,SAAQ;;;;;;0BACnD,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAM,MAAK;gBAAQ,SAAQ;;;;;;;;;;;;AAG3D;KAnDgB;AAqDT,SAAS,eAAe,KAAqC;QAArC,EAAE,SAAS,EAA0B,GAArC;IAC7B,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,2BAA2B;;0BAC5C,6LAAC;gBAAW,MAAK;;;;;;0BACjB,6LAAC;gBAAK,WAAU;0BAA8F;;;;;;;;;;;;AAKpH;MATgB", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/official-kaizen-project/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { \n  Home, \n  BarChart3, \n  GraduationCap, \n  Bot, \n  Bell, \n  Settings,\n  Sparkles\n} from 'lucide-react'\nimport { KaizenWordmark } from '@/components/ui/kaizen-logo'\nimport { cn } from '@/lib/utils'\n\ninterface NavItem {\n  id: string\n  label: string\n  icon: React.ComponentType<{ className?: string }>\n  isNew?: boolean\n  badge?: string\n}\n\nconst navItems: NavItem[] = [\n  { id: 'home', label: 'Home', icon: Home },\n  { id: 'analytics', label: 'Analytics', icon: BarChart3 },\n  { id: 'education', label: 'Education', icon: GraduationCap },\n  { id: 'kaizen-ai', label: 'Kaizen AI', icon: Bot, isNew: true },\n  { id: 'alerts', label: 'Alerts', icon: Bell, badge: '3' },\n  { id: 'settings', label: 'Settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [activeItem, setActiveItem] = useState('kaizen-ai')\n\n  return (\n    <div className=\"bg-slate-900/50 border-r border-slate-800/50 backdrop-blur-xl\">\n      {/* Sidebar content */}\n      <div className=\"flex flex-col h-full\">\n        {/* Logo section */}\n        <div className=\"p-6 border-b border-slate-800/30\">\n          <KaizenWordmark />\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 p-4 space-y-2\">\n          {navItems.map((item) => {\n            const Icon = item.icon\n            const isActive = activeItem === item.id\n            \n            return (\n              <button\n                key={item.id}\n                onClick={() => setActiveItem(item.id)}\n                className={cn(\n                  'w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-200 group relative',\n                  'hover:bg-slate-800/40 hover:shadow-lg hover:shadow-blue-500/10',\n                  'focus:outline-none focus:ring-2 focus:ring-blue-500/20',\n                  isActive && [\n                    'bg-gradient-to-r from-blue-600/20 to-blue-500/10',\n                    'border border-blue-500/20',\n                    'shadow-lg shadow-blue-500/20',\n                    'text-blue-100'\n                  ],\n                  !isActive && 'text-slate-300 hover:text-white'\n                )}\n              >\n                {/* Active indicator */}\n                {isActive && (\n                  <div className=\"absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-400 to-blue-600 rounded-r-full shadow-lg shadow-blue-500/50\" />\n                )}\n                \n                {/* Icon with glow effect */}\n                <div className={cn(\n                  'relative p-2 rounded-lg transition-all duration-200',\n                  isActive && 'bg-blue-500/20 shadow-inner',\n                  !isActive && 'group-hover:bg-slate-700/50'\n                )}>\n                  <Icon className={cn(\n                    'h-5 w-5 transition-all duration-200',\n                    isActive && 'text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.5)]',\n                    !isActive && 'group-hover:text-blue-300'\n                  )} />\n                </div>\n\n                {/* Label */}\n                <span className={cn(\n                  'font-medium transition-all duration-200',\n                  isActive && 'font-semibold'\n                )}>\n                  {item.label}\n                </span>\n\n                {/* New badge */}\n                {item.isNew && (\n                  <div className=\"ml-auto flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-emerald-500 to-emerald-400 rounded-full text-xs font-bold text-white shadow-lg shadow-emerald-500/30\">\n                    <Sparkles className=\"h-3 w-3\" />\n                    New\n                  </div>\n                )}\n\n                {/* Notification badge */}\n                {item.badge && (\n                  <div className=\"ml-auto w-6 h-6 bg-gradient-to-r from-red-500 to-red-400 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg shadow-red-500/30\">\n                    {item.badge}\n                  </div>\n                )}\n              </button>\n            )\n          })}\n        </nav>\n\n        {/* Bottom section */}\n        <div className=\"p-4 border-t border-slate-800/30\">\n          <div className=\"bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-xl p-4 border border-slate-700/50 shadow-inner\">\n            <div className=\"text-sm font-medium text-slate-200 mb-2\">Kaizen AI for Platinum</div>\n            <div className=\"text-xs text-slate-400 mb-3\">Unlock advanced AI features</div>\n            <button className=\"w-full bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-[1.02]\">\n              Join Now\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAbA;;;;;AAuBA,MAAM,WAAsB;IAC1B;QAAE,IAAI;QAAQ,OAAO;QAAQ,MAAM,8MAAI;IAAC;IACxC;QAAE,IAAI;QAAa,OAAO;QAAa,MAAM,kOAAS;IAAC;IACvD;QAAE,IAAI;QAAa,OAAO;QAAa,MAAM,4OAAa;IAAC;IAC3D;QAAE,IAAI;QAAa,OAAO;QAAa,MAAM,0MAAG;QAAE,OAAO;IAAK;IAC9D;QAAE,IAAI;QAAU,OAAO;QAAU,MAAM,6MAAI;QAAE,OAAO;IAAI;IACxD;QAAE,IAAI;QAAY,OAAO;QAAY,MAAM,yNAAQ;IAAC;CACrD;AAEM,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAc;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,eAAe,KAAK,EAAE;wBAEvC,qBACE,6LAAC;4BAEC,SAAS,IAAM,cAAc,KAAK,EAAE;4BACpC,WAAW,IAAA,4HAAE,EACX,4GACA,kEACA,0DACA,YAAY;gCACV;gCACA;gCACA;gCACA;6BACD,EACD,CAAC,YAAY;;gCAId,0BACC,6LAAC;oCAAI,WAAU;;;;;;8CAIjB,6LAAC;oCAAI,WAAW,IAAA,4HAAE,EAChB,uDACA,YAAY,+BACZ,CAAC,YAAY;8CAEb,cAAA,6LAAC;wCAAK,WAAW,IAAA,4HAAE,EACjB,uCACA,YAAY,4DACZ,CAAC,YAAY;;;;;;;;;;;8CAKjB,6LAAC;oCAAK,WAAW,IAAA,4HAAE,EACjB,2CACA,YAAY;8CAEX,KAAK,KAAK;;;;;;gCAIZ,KAAK,KAAK,kBACT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yNAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;gCAMnC,KAAK,KAAK,kBACT,6LAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BApDV,KAAK,EAAE;;;;;oBAyDlB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA0C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;gCAAO,WAAU;0CAA2O;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzQ;GA7FgB;KAAA", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/official-kaizen-project/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { MessageSquare, User } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport function Header() {\n  return (\n    <header className=\"h-16 border-b border-slate-800/30 bg-slate-900/30 backdrop-blur-xl\">\n      <div className=\"flex items-center justify-between h-full px-6\">\n        {/* Page title */}\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-slate-800/50 rounded-lg border border-slate-700/50\">\n            <MessageSquare className=\"h-5 w-5 text-blue-400\" />\n          </div>\n          <div>\n            <h1 className=\"text-lg font-semibold text-white\">Breakout Model RR</h1>\n            <p className=\"text-sm text-slate-400\">AI-powered trading insights</p>\n          </div>\n        </div>\n\n        {/* Right section */}\n        <div className=\"flex items-center gap-4\">\n          {/* New Chat button */}\n          <button className=\"bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-[1.02] flex items-center gap-2\">\n            <MessageSquare className=\"h-4 w-4\" />\n            New Chat\n          </button>\n\n          {/* User profile */}\n          <div className=\"flex items-center gap-3 p-2 rounded-lg hover:bg-slate-800/30 transition-colors cursor-pointer\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg\">\n              <User className=\"h-4 w-4 text-white\" />\n            </div>\n            <div className=\"text-sm\">\n              <div className=\"font-medium text-white\">Cole Lane</div>\n              <div className=\"text-slate-400\">Principal</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAHA;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4OAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAK1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC,4OAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAyB;;;;;;sDACxC,6LAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;KArCgB", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/official-kaizen-project/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { cn } from '@/lib/utils'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function MainLayout({ children, className }: MainLayoutProps) {\n  return (\n    <div className={cn('min-h-screen bg-slate-950 text-white', className)}>\n      {/* Main grid layout */}\n      <div className=\"grid grid-cols-[280px_1fr] h-screen\">\n        {/* Sidebar */}\n        <Sidebar />\n        \n        {/* Main content area */}\n        <div className=\"flex flex-col overflow-hidden\">\n          {/* Header */}\n          <Header />\n          \n          {/* Content */}\n          <main className=\"flex-1 overflow-auto\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AALA;;;;;AAYO,SAAS,WAAW,KAAwC;QAAxC,EAAE,QAAQ,EAAE,SAAS,EAAmB,GAAxC;IACzB,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,wCAAwC;kBAEzD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,qJAAO;;;;;8BAGR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mJAAM;;;;;sCAGP,6LAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb;KArBgB", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/official-kaizen-project/src/components/chat/chat-interface.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Send, ThumbsUp, ThumbsDown, Copy, MoreHorizontal, TrendingUp, AlertTriangle } from 'lucide-react'\nimport { <PERSON>zenLogo } from '@/components/ui/kaizen-logo'\nimport { cn } from '@/lib/utils'\n\ninterface ChatMessage {\n  id: string\n  type: 'user' | 'assistant'\n  content: string\n  timestamp: Date\n  reactions?: { likes: number; dislikes: number }\n}\n\nconst sampleMessages: ChatMessage[] = [\n  {\n    id: '1',\n    type: 'user',\n    content: 'How does RR work?',\n    timestamp: new Date(),\n  },\n  {\n    id: '2',\n    type: 'assistant',\n    content: 'Risk to reward ratio in finance, especially in trading, is a way to compare the potential profit of a trade against its potential loss. It helps investors assess whether the potential gains justify the potential risks involved.',\n    timestamp: new Date(),\n    reactions: { likes: 0, dislikes: 0 }\n  },\n  {\n    id: '3',\n    type: 'user',\n    content: 'Statistically, what RR works best with my model?',\n    timestamp: new Date(),\n  },\n  {\n    id: '4',\n    type: 'assistant',\n    content: 'For your breakout model, the best option would be a 2:1 RR. Over the course of a year, you\\'d profit $78,000 with your standard 1% risk. Your YTD win rate would be 65%.\\n\\nWant me to gather more data?',\n    timestamp: new Date(),\n    reactions: { likes: 0, dislikes: 0 }\n  },\n  {\n    id: '5',\n    type: 'user',\n    content: 'Yes! how do I leverage this info in my trading?',\n    timestamp: new Date(),\n  },\n  {\n    id: '6',\n    type: 'assistant',\n    content: 'Backtesting 1,200 breakout trades shows a 2:1 risk-reward ratio performs best: ~65% win rate and 32-38% annualized return at 1% risk per trade.\\n\\nAlternatives: 1.5:1 RR wins more often (~72%) but profits less; 3:1 RR wins less (~50%) but has bigger payouts. Key tip: use valid stop levels, journal 50+ trades, and slightly scale size in trending markets (win rate can rise to 70%).',\n    timestamp: new Date(),\n    reactions: { likes: 0, dislikes: 0 }\n  }\n]\n\nexport function ChatInterface() {\n  const [messages] = useState<ChatMessage[]>(sampleMessages)\n  const [inputValue, setInputValue] = useState('')\n\n  const handleSend = () => {\n    if (inputValue.trim()) {\n      // Handle sending message\n      setInputValue('')\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-slate-950\">\n      {/* Chat messages */}\n      <div className=\"flex-1 overflow-auto p-6 space-y-6\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={cn(\n              'flex gap-4',\n              message.type === 'user' ? 'justify-end' : 'justify-start'\n            )}\n          >\n            {message.type === 'assistant' && (\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/25\">\n                  <KaizenLogo size=\"sm\" className=\"text-white\" />\n                </div>\n              </div>\n            )}\n\n            <div className={cn(\n              'max-w-2xl',\n              message.type === 'user' ? 'order-2' : 'order-1'\n            )}>\n              {/* Message bubble */}\n              <div className={cn(\n                'rounded-2xl px-6 py-4 shadow-lg',\n                message.type === 'user' \n                  ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-blue-500/25' \n                  : 'bg-slate-800/50 border border-slate-700/50 text-slate-100 backdrop-blur-sm'\n              )}>\n                <p className=\"text-sm leading-relaxed whitespace-pre-wrap\">\n                  {message.content}\n                </p>\n              </div>\n\n              {/* Message actions (for assistant messages) */}\n              {message.type === 'assistant' && (\n                <div className=\"flex items-center gap-2 mt-3 px-2\">\n                  <button className=\"p-2 hover:bg-slate-800/50 rounded-lg transition-colors group\">\n                    <ThumbsUp className=\"h-4 w-4 text-slate-400 group-hover:text-green-400 transition-colors\" />\n                  </button>\n                  <button className=\"p-2 hover:bg-slate-800/50 rounded-lg transition-colors group\">\n                    <ThumbsDown className=\"h-4 w-4 text-slate-400 group-hover:text-red-400 transition-colors\" />\n                  </button>\n                  <button className=\"p-2 hover:bg-slate-800/50 rounded-lg transition-colors group\">\n                    <Copy className=\"h-4 w-4 text-slate-400 group-hover:text-blue-400 transition-colors\" />\n                  </button>\n                  <button className=\"p-2 hover:bg-slate-800/50 rounded-lg transition-colors group\">\n                    <MoreHorizontal className=\"h-4 w-4 text-slate-400 group-hover:text-slate-300 transition-colors\" />\n                  </button>\n                </div>\n              )}\n            </div>\n\n            {message.type === 'user' && (\n              <div className=\"flex-shrink-0 order-3\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-medium shadow-lg\">\n                  CL\n                </div>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Input area */}\n      <div className=\"border-t border-slate-800/30 bg-slate-900/30 backdrop-blur-xl p-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"relative\">\n            <textarea\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              placeholder=\"Ask Kaizen AI anything...\"\n              className=\"w-full bg-slate-800/50 border border-slate-700/50 rounded-2xl px-6 py-4 pr-16 text-white placeholder-slate-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/30 transition-all duration-200 shadow-inner\"\n              rows={1}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' && !e.shiftKey) {\n                  e.preventDefault()\n                  handleSend()\n                }\n              }}\n            />\n            <button\n              onClick={handleSend}\n              disabled={!inputValue.trim()}\n              className=\"absolute right-3 top-1/2 -translate-y-1/2 p-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 disabled:from-slate-600 disabled:to-slate-500 text-white rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 disabled:shadow-none hover:scale-105 disabled:scale-100\"\n            >\n              <Send className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          {/* Quick actions */}\n          <div className=\"flex items-center gap-3 mt-4\">\n            <button className=\"flex items-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 rounded-lg text-sm text-slate-300 hover:text-white transition-all duration-200\">\n              <TrendingUp className=\"h-4 w-4\" />\n              Market Analysis\n            </button>\n            <button className=\"flex items-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 rounded-lg text-sm text-slate-300 hover:text-white transition-all duration-200\">\n              <AlertTriangle className=\"h-4 w-4\" />\n              Risk Assessment\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAeA,MAAM,iBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,WAAW,IAAI;QACf,WAAW;YAAE,OAAO;YAAG,UAAU;QAAE;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,WAAW,IAAI;QACf,WAAW;YAAE,OAAO;YAAG,UAAU;QAAE;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,WAAW,IAAI;QACf,WAAW;YAAE,OAAO;YAAG,UAAU;QAAE;IACrC;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,IAAI;YACrB,yBAAyB;YACzB,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAEC,WAAW,IAAA,4HAAE,EACX,cACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;;4BAG3C,QAAQ,IAAI,KAAK,6BAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2JAAU;wCAAC,MAAK;wCAAK,WAAU;;;;;;;;;;;;;;;;0CAKtC,6LAAC;gCAAI,WAAW,IAAA,4HAAE,EAChB,aACA,QAAQ,IAAI,KAAK,SAAS,YAAY;;kDAGtC,6LAAC;wCAAI,WAAW,IAAA,4HAAE,EAChB,mCACA,QAAQ,IAAI,KAAK,SACb,6EACA;kDAEJ,cAAA,6LAAC;4CAAE,WAAU;sDACV,QAAQ,OAAO;;;;;;;;;;;oCAKnB,QAAQ,IAAI,KAAK,6BAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6NAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,mOAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6MAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,qOAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAMjC,QAAQ,IAAI,KAAK,wBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAwI;;;;;;;;;;;;uBAnDtJ,QAAQ,EAAE;;;;;;;;;;0BA6DrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aAAY;oCACZ,WAAU;oCACV,MAAM;oCACN,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;4CACpC,EAAE,cAAc;4CAChB;wCACF;oCACF;;;;;;8CAEF,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC,WAAW,IAAI;oCAC1B,WAAU;8CAEV,cAAA,6LAAC,6MAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,mOAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGpC,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,4OAAa;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GAtHgB;KAAA", "debugId": null}}]}