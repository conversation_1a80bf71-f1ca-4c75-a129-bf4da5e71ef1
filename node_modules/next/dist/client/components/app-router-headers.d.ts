export declare const RSC_HEADER: "rsc";
export declare const ACTION_HEADER: "next-action";
export declare const NEXT_ROUTER_STATE_TREE_HEADER: "next-router-state-tree";
export declare const NEXT_ROUTER_PREFETCH_HEADER: "next-router-prefetch";
export declare const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: "next-router-segment-prefetch";
export declare const NEXT_HMR_REFRESH_HEADER: "next-hmr-refresh";
export declare const NEXT_HMR_REFRESH_HASH_COOKIE: "__next_hmr_refresh_hash__";
export declare const NEXT_URL: "next-url";
export declare const RSC_CONTENT_TYPE_HEADER: "text/x-component";
export declare const FLIGHT_HEADERS: readonly ["rsc", "next-router-state-tree", "next-router-prefetch", "next-hmr-refresh", "next-router-segment-prefetch"];
export declare const NEXT_RSC_UNION_QUERY: "_rsc";
export declare const NEXT_ROUTER_STALE_TIME_HEADER: "x-nextjs-stale-time";
export declare const NEXT_DID_POSTPONE_HEADER: "x-nextjs-postponed";
export declare const NEXT_REWRITTEN_PATH_HEADER: "x-nextjs-rewritten-path";
export declare const NEXT_REWRITTEN_QUERY_HEADER: "x-nextjs-rewritten-query";
export declare const NEXT_IS_PRERENDER_HEADER: "x-nextjs-prerender";
export declare const NEXT_ACTION_NOT_FOUND_HEADER: "x-nextjs-action-not-found";
