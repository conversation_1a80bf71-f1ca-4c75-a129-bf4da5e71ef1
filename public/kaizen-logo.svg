<svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="kaizenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="50%" stop-color="#1D4ED8" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="url(#kaizenGradient)" filter="url(#glow)" />
  
  <!-- Inner design - stylized "K" and "AI" -->
  <g fill="white">
    <!-- K letter -->
    <path d="M25 25 L25 75 L30 75 L30 52 L42 75 L48 75 L35 50 L47 25 L41 25 L30 45 L30 25 Z" />
    
    <!-- AI letters -->
    <path d="M55 25 L50 75 L55 75 L56 65 L64 65 L65 75 L70 75 L65 25 L55 25 Z M57 35 L63 55 L57 55 L57 35 Z" />
    <rect x="75" y="25" width="5" height="50" />
    <rect x="75" y="45" width="10" height="5" />
  </g>
  
  <!-- Accent dots -->
  <circle cx="20" cy="20" r="2" fill="white" opacity="0.8" />
  <circle cx="80" cy="80" r="2" fill="white" opacity="0.8" />
  <circle cx="80" cy="20" r="1.5" fill="white" opacity="0.6" />
</svg>
