'use client'

import React, { useState } from 'react'
import { Send, ThumbsUp, ThumbsDown, Copy, MoreHorizontal, TrendingUp, AlertTriangle } from 'lucide-react'
import { <PERSON>zenLogo } from '@/components/ui/kaizen-logo'
import { cn } from '@/lib/utils'

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  reactions?: { likes: number; dislikes: number }
}

const sampleMessages: ChatMessage[] = [
  {
    id: '1',
    type: 'user',
    content: 'How does RR work?',
    timestamp: new Date(),
  },
  {
    id: '2',
    type: 'assistant',
    content: 'Risk to reward ratio in finance, especially in trading, is a way to compare the potential profit of a trade against its potential loss. It helps investors assess whether the potential gains justify the potential risks involved.',
    timestamp: new Date(),
    reactions: { likes: 0, dislikes: 0 }
  },
  {
    id: '3',
    type: 'user',
    content: 'Statistically, what RR works best with my model?',
    timestamp: new Date(),
  },
  {
    id: '4',
    type: 'assistant',
    content: 'For your breakout model, the best option would be a 2:1 RR. Over the course of a year, you\'d profit $78,000 with your standard 1% risk. Your YTD win rate would be 65%.\n\nWant me to gather more data?',
    timestamp: new Date(),
    reactions: { likes: 0, dislikes: 0 }
  },
  {
    id: '5',
    type: 'user',
    content: 'Yes! how do I leverage this info in my trading?',
    timestamp: new Date(),
  },
  {
    id: '6',
    type: 'assistant',
    content: 'Backtesting 1,200 breakout trades shows a 2:1 risk-reward ratio performs best: ~65% win rate and 32-38% annualized return at 1% risk per trade.\n\nAlternatives: 1.5:1 RR wins more often (~72%) but profits less; 3:1 RR wins less (~50%) but has bigger payouts. Key tip: use valid stop levels, journal 50+ trades, and slightly scale size in trending markets (win rate can rise to 70%).',
    timestamp: new Date(),
    reactions: { likes: 0, dislikes: 0 }
  }
]

export function ChatInterface() {
  const [messages] = useState<ChatMessage[]>(sampleMessages)
  const [inputValue, setInputValue] = useState('')

  const handleSend = () => {
    if (inputValue.trim()) {
      // Handle sending message
      setInputValue('')
    }
  }

  return (
    <div className="flex flex-col h-full bg-slate-950">
      {/* Chat messages */}
      <div className="flex-1 overflow-auto p-6 space-y-6">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex gap-4',
              message.type === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            {message.type === 'assistant' && (
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <KaizenLogo size="sm" className="text-white" />
                </div>
              </div>
            )}

            <div className={cn(
              'max-w-2xl',
              message.type === 'user' ? 'order-2' : 'order-1'
            )}>
              {/* Message bubble */}
              <div className={cn(
                'rounded-2xl px-6 py-4 shadow-lg',
                message.type === 'user' 
                  ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-blue-500/25' 
                  : 'bg-slate-800/50 border border-slate-700/50 text-slate-100 backdrop-blur-sm'
              )}>
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </p>
              </div>

              {/* Message actions (for assistant messages) */}
              {message.type === 'assistant' && (
                <div className="flex items-center gap-2 mt-3 px-2">
                  <button className="p-2 hover:bg-slate-800/50 rounded-lg transition-colors group">
                    <ThumbsUp className="h-4 w-4 text-slate-400 group-hover:text-green-400 transition-colors" />
                  </button>
                  <button className="p-2 hover:bg-slate-800/50 rounded-lg transition-colors group">
                    <ThumbsDown className="h-4 w-4 text-slate-400 group-hover:text-red-400 transition-colors" />
                  </button>
                  <button className="p-2 hover:bg-slate-800/50 rounded-lg transition-colors group">
                    <Copy className="h-4 w-4 text-slate-400 group-hover:text-blue-400 transition-colors" />
                  </button>
                  <button className="p-2 hover:bg-slate-800/50 rounded-lg transition-colors group">
                    <MoreHorizontal className="h-4 w-4 text-slate-400 group-hover:text-slate-300 transition-colors" />
                  </button>
                </div>
              )}
            </div>

            {message.type === 'user' && (
              <div className="flex-shrink-0 order-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-medium shadow-lg">
                  CL
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Input area */}
      <div className="border-t border-slate-800/30 bg-slate-900/30 backdrop-blur-xl p-6">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask Kaizen AI anything..."
              className="w-full bg-slate-800/50 border border-slate-700/50 rounded-2xl px-6 py-4 pr-16 text-white placeholder-slate-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/30 transition-all duration-200 shadow-inner"
              rows={1}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSend()
                }
              }}
            />
            <button
              onClick={handleSend}
              disabled={!inputValue.trim()}
              className="absolute right-3 top-1/2 -translate-y-1/2 p-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 disabled:from-slate-600 disabled:to-slate-500 text-white rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 disabled:shadow-none hover:scale-105 disabled:scale-100"
            >
              <Send className="h-4 w-4" />
            </button>
          </div>

          {/* Quick actions */}
          <div className="flex items-center gap-3 mt-4">
            <button className="flex items-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 rounded-lg text-sm text-slate-300 hover:text-white transition-all duration-200">
              <TrendingUp className="h-4 w-4" />
              Market Analysis
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/50 rounded-lg text-sm text-slate-300 hover:text-white transition-all duration-200">
              <AlertTriangle className="h-4 w-4" />
              Risk Assessment
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
