'use client'

import React from 'react'
import { MessageSquare, User } from 'lucide-react'
import { cn } from '@/lib/utils'

export function Header() {
  return (
    <header className="h-16 border-b border-slate-800/30 bg-slate-900/30 backdrop-blur-xl">
      <div className="flex items-center justify-between h-full px-6">
        {/* Page title */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-slate-800/50 rounded-lg border border-slate-700/50">
            <MessageSquare className="h-5 w-5 text-blue-400" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-white">Breakout Model RR</h1>
            <p className="text-sm text-slate-400">AI-powered trading insights</p>
          </div>
        </div>

        {/* Right section */}
        <div className="flex items-center gap-4">
          {/* New Chat button */}
          <button className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-[1.02] flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            New Chat
          </button>

          {/* User profile */}
          <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-slate-800/30 transition-colors cursor-pointer">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
              <User className="h-4 w-4 text-white" />
            </div>
            <div className="text-sm">
              <div className="font-medium text-white">Cole Lane</div>
              <div className="text-slate-400">Principal</div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
