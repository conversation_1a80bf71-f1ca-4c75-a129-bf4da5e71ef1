'use client'

import React, { useState } from 'react'
import { 
  Home, 
  BarChart3, 
  GraduationCap, 
  Bot, 
  Bell, 
  Settings,
  Sparkles
} from 'lucide-react'
import { KaizenWordmark } from '@/components/ui/kaizen-logo'
import { cn } from '@/lib/utils'

interface NavItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  isNew?: boolean
  badge?: string
}

const navItems: NavItem[] = [
  { id: 'home', label: 'Home', icon: Home },
  { id: 'analytics', label: 'Analytics', icon: BarChart3 },
  { id: 'education', label: 'Education', icon: GraduationCap },
  { id: 'kaizen-ai', label: 'Kaizen AI', icon: Bot, isNew: true },
  { id: 'alerts', label: 'Alerts', icon: Bell, badge: '3' },
  { id: 'settings', label: 'Settings', icon: Settings },
]

export function Sidebar() {
  const [activeItem, setActiveItem] = useState('kaizen-ai')

  return (
    <div className="bg-slate-900/50 border-r border-slate-800/50 backdrop-blur-xl">
      {/* Sidebar content */}
      <div className="flex flex-col h-full">
        {/* Logo section */}
        <div className="p-6 border-b border-slate-800/30">
          <KaizenWordmark />
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon
            const isActive = activeItem === item.id
            
            return (
              <button
                key={item.id}
                onClick={() => setActiveItem(item.id)}
                className={cn(
                  'w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-200 group relative',
                  'hover:bg-slate-800/40 hover:shadow-lg hover:shadow-blue-500/10',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                  isActive && [
                    'bg-gradient-to-r from-blue-600/20 to-blue-500/10',
                    'border border-blue-500/20',
                    'shadow-lg shadow-blue-500/20',
                    'text-blue-100'
                  ],
                  !isActive && 'text-slate-300 hover:text-white'
                )}
              >
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-400 to-blue-600 rounded-r-full shadow-lg shadow-blue-500/50" />
                )}
                
                {/* Icon with glow effect */}
                <div className={cn(
                  'relative p-2 rounded-lg transition-all duration-200',
                  isActive && 'bg-blue-500/20 shadow-inner',
                  !isActive && 'group-hover:bg-slate-700/50'
                )}>
                  <Icon className={cn(
                    'h-5 w-5 transition-all duration-200',
                    isActive && 'text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.5)]',
                    !isActive && 'group-hover:text-blue-300'
                  )} />
                </div>

                {/* Label */}
                <span className={cn(
                  'font-medium transition-all duration-200',
                  isActive && 'font-semibold'
                )}>
                  {item.label}
                </span>

                {/* New badge */}
                {item.isNew && (
                  <div className="ml-auto flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-emerald-500 to-emerald-400 rounded-full text-xs font-bold text-white shadow-lg shadow-emerald-500/30">
                    <Sparkles className="h-3 w-3" />
                    New
                  </div>
                )}

                {/* Notification badge */}
                {item.badge && (
                  <div className="ml-auto w-6 h-6 bg-gradient-to-r from-red-500 to-red-400 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg shadow-red-500/30">
                    {item.badge}
                  </div>
                )}
              </button>
            )
          })}
        </nav>

        {/* Bottom section */}
        <div className="p-4 border-t border-slate-800/30">
          <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-xl p-4 border border-slate-700/50 shadow-inner">
            <div className="text-sm font-medium text-slate-200 mb-2">Kaizen AI for Platinum</div>
            <div className="text-xs text-slate-400 mb-3">Unlock advanced AI features</div>
            <button className="w-full bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-[1.02]">
              Join Now
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
